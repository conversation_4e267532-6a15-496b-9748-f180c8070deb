import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core';
import { JwtModule } from '@nestjs/jwt';
import { ThrottlerModule, ThrottlerGuard } from '@nestjs/throttler';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { VectorDBModule } from './modules/vector-db/vector-db.module';
import { DocumentsModule } from './modules/documents/documents.module';
import { EmbeddingModule } from './modules/embedding/embedding.module';
import jwtConfig from './core/configs/jwt/jwt.config';
import questionPoolConfig from './core/configs/question-pool/question-pool.config';
import { AuthModule } from './modules/auth/auth.module';
import { UserModule } from './modules/user/user.module';
import { JwtStrategy } from './modules/auth/strategies/jwt.strategy';
import { PromptModule } from './modules/prompt/prompt.module';
import { ResponseFormatInterceptor } from './core/interceptors/response.interceptor';
import { AuthGuard } from './modules/auth/guards/auth.guard';
import { DatabaseModule } from './modules/database/database.module';
import typeormConfig from './core/configs/database/typeorm.config';
import { OptionsModule } from './modules/options/options.module';
import { WorksheetModule } from './modules/worksheet/worksheet.module';
import { GenImageModule } from './modules/gen-image/gen-image.module';
import { AiModule } from './modules/ai/ai.module';
import { RedisModule } from './modules/redis/redis.module';
import { BullModule } from '@nestjs/bullmq';
import { MongodbModule } from './modules/mongodb/mongodb.module';
import { FilesModule } from './modules/files/files.module';
import { ScheduleModule } from '@nestjs/schedule';
import { MultimodalModule } from './modules/multimodal/multimodal.module';
import { ExamModule } from './modules/exam/exam.module';
import { QuestionPoolModule } from './modules/question-pool/question-pool.module';
import { BrandModule } from './modules/brand/brand.module';
import { SchoolModule } from './modules/school/school.module';
import { MonitoringModule } from './modules/monitoring/monitoring.module';
import { StripeModule } from './modules/stripe/stripe.module';
import { PackagesModule } from './modules/packages/packages.module';
import { StripeController } from './modules/stripe/stripe.controller';
import { PriceModule } from './modules/price/price.module';
import { SubscriptionModule } from './modules/subscription/subscription.module';
import { SeedsModule } from './database/seeds/seeds.module';
import { MailModule } from './mail/mail.module';
import { UsageTrackingModule } from './modules/usage-tracking/usage-tracking.module';
import { UsageModule } from './modules/usage/usage.module';

@Module({
  imports: [
    StripeModule,
    ScheduleModule.forRoot(),
    ConfigModule.forRoot({
      isGlobal: true,
      load: [typeormConfig, jwtConfig, questionPoolConfig],
    }),
    ThrottlerModule.forRoot([
      {
        name: 'default',
        ttl: 60000, // 1 minute
        limit: 2000, // Default limit for non-worksheet endpoints
      },
      {
        name: 'auth',
        ttl: 900000, // 15 minutes
        limit: 1000, // Strict limit for password reset endpoints
      },
    ]),
    BullModule.forRoot({
      connection: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
      },
    }),
    RedisModule.forRootAsync({
      useFactory: () => {
        return [
          {
            name: 'COMMON_CACHE_NAME',
            host: process.env.REDIS_HOST || 'localhost',
            port: parseInt(process.env.REDIS_PORT || '6379'),
            url: process.env.REDIS_COMMON,
          },
        ];
      },
    }),
    DatabaseModule,
    JwtModule.registerAsync(jwtConfig.asProvider()),
    UserModule,
    AuthModule,
    DocumentsModule,
    VectorDBModule,
    EmbeddingModule,
    PromptModule,
    OptionsModule,
    WorksheetModule,
    GenImageModule,
    AiModule,
    MongodbModule,
    FilesModule,
    MultimodalModule,
    ExamModule,
    QuestionPoolModule,
    BrandModule,
    SchoolModule,
    MonitoringModule,
    PackagesModule,
    PriceModule,
    SubscriptionModule,
    SeedsModule,
    MailModule,
    UsageTrackingModule,
    UsageModule,
  ],
  controllers: [AppController, StripeController],
  providers: [
    AppService,
    JwtStrategy,
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseFormatInterceptor,
    },
  ],
})
export class AppModule {}
